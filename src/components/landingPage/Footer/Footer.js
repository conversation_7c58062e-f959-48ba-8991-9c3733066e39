import React from 'react';
import { useNavigate } from 'react-router-dom';
import './Footer.css';

// SVG Icons
const TwitterIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
  </svg>
);

const LinkedInIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
  </svg>
);

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const navigate = useNavigate();

  const footerLinks = {
    product: [
      { name: 'Features', href: '#features' },
      { name: 'Pricing', href: '#pricing' },
    ],
    company: [
      { name: 'About Us', href: 'https://blogs.theinfiniai.live/about-the-infini-ai', isRoute: true},
      { name: 'Blog', href: 'https://blogs.theinfiniai.live/', isRoute: true },
      { name: 'Careers', href: '/careers', isRoute: true },
    ],
    support: [
      { name: 'Help Center', href: '/support', isRoute: true },
      { name: 'Support Tickets', href: '/support', isRoute: true },
    ],
    legal: [
      { name: 'Privacy Policy', href: 'https://blogs.theinfiniai.live/privacy-policy', isRoute: true },
      { name: 'Terms of Service', href: 'https://blogs.theinfiniai.live/terms-and-conditions', isRoute: true },
    ]
  };

  const socialLinks = [
    { name: 'Twitter', icon: <TwitterIcon />, href: 'https://twitter.com/theinfiniai' },
    { name: 'LinkedIn', icon: <LinkedInIcon />, href: 'https://linkedin.com/company/theinfiniai' },
  ];

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId.replace('#', ''));
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleLinkClick = (link) => {
    if (link.isRoute) {
      // Check if it's an external URL
      if (link.href.startsWith('http://') || link.href.startsWith('https://')) {
        window.open(link.href, '_blank', 'noopener,noreferrer');
      } else {
        navigate(link.href);
      }
    } else {
      scrollToSection(link.href);
    }
  };

  const handleSocialClick = (href) => {
    window.open(href, '_blank', 'noopener,noreferrer');
  };

  return (
    <footer id="contact" className="footer">
      <div className="container">
        <div className="footer__content">
          <div className="footer__main">
            <div className="footer__brand">
              <div className="footer__logo">
                <img
                  src="/assets/images/infini-logo.svg"
                  alt="the infini ai"
                  className="footer__logo-img"
                />
              </div>
              <p className="footer__description">
                Empowering conversations with advanced AI technology.
                Experience the future of intelligent interactions today.
              </p>
              <div className="footer__social">
                {socialLinks.map((social, index) => (
                  <button
                    key={index}
                    onClick={() => handleSocialClick(social.href)}
                    className="footer__social-link"
                    aria-label={social.name}
                  >
                    {social.icon}
                  </button>
                ))}
              </div>
            </div>

            <div className="footer__links">
              <div className="footer__link-group">
                <h4 className="footer__link-title">Product</h4>
                <ul className="footer__link-list">
                  {footerLinks.product.map((link, index) => (
                    <li key={index}>
                      <button
                        onClick={() => scrollToSection(link.href)}
                        className="footer__link"
                      >
                        {link.name}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="footer__link-group">
                <h4 className="footer__link-title">Company</h4>
                <ul className="footer__link-list">
                  {footerLinks.company.map((link, index) => (
                    <li key={index}>
                      <button
                        onClick={() => handleLinkClick(link)}
                        className="footer__link"
                      >
                        {link.name}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="footer__link-group">
                <h4 className="footer__link-title">Support</h4>
                <ul className="footer__link-list">
                  {footerLinks.support.map((link, index) => (
                    <li key={index}>
                      <button
                        onClick={() => handleLinkClick(link)}
                        className="footer__link"
                      >
                        {link.name}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="footer__link-group">
                <h4 className="footer__link-title">Legal</h4>
                <ul className="footer__link-list">
                  {footerLinks.legal.map((link, index) => (
                    <li key={index}>
                      <button
                        onClick={() => handleLinkClick(link)}
                        className="footer__link"
                      >
                        {link.name}
                      </button>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>

          <div className="footer__newsletter">
            <div className="footer__newsletter-content">
              <h4 className="footer__newsletter-title">Stay Updated</h4>
              <p className="footer__newsletter-description">
                Get the latest updates on new features and AI advancements.
              </p>
              <div className="footer__newsletter-form">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="footer__newsletter-input"
                />
                <button className="btn btn-primary footer__newsletter-btn">
                  Subscribe
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="footer__bottom">
          <div className="footer__bottom-content">
            <p className="footer__copyright">
              © {currentYear} the infini ai. All rights reserved.
            </p>

          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
