.chat-input {
  max-width: 900px;
  margin: 0 auto;
  width: 100%;
}

.chat-input__form {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 24px;
  padding: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.chat-input__container {
  display: flex;
  align-items: flex-end;
  gap: 16px;
}

.chat-input__field-wrapper {
  flex: 1;
  display: flex;
}

.chat-input__field {
  flex: 1;
  background: none;
  border: none;
  color: var(--text-primary);
  font-size: 15px;
  line-height: 1.6;
  resize: none;
  outline: none;
  min-height: 28px;
  max-height: 140px;
  overflow-y: auto;
  font-family: inherit;
  padding: 4px 0;
}

.chat-input__field::placeholder {
  color: var(--text-muted);
}

.chat-input__field::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

.chat-input__field::-webkit-scrollbar-track {
  background: transparent;
}

.chat-input__field::-webkit-scrollbar-thumb {
  background: transparent;
}

.chat-input__actions {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 10px;
  padding-top: 5px;
  border-top: 1px solid var(--border-color);
  flex-wrap: wrap;
}

.chat-input__model-selector {
  margin-left: auto;
}

.chat-input__file-hint {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  margin-top: 8px;
  background: var(--info-bg, #e3f2fd);
  border: 1px solid var(--info-border, #bbdefb);
  border-radius: 8px;
  font-size: 12px;
  color: var(--info-text, #1976d2);
}

.chat-input__file-hint-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.chat-input__file-hint-text {
  line-height: 1.4;
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .chat-input__file-hint {
    background: rgba(33, 150, 243, 0.1);
    border-color: rgba(33, 150, 243, 0.3);
    color: #64b5f6;
  }
}

.chat-input__action-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 10px 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 15px;
  font-weight: 500;
}

.chat-input__action-btn:hover {
  background-color: var(--secondary-bg);
  color: var(--text-primary);
}

.chat-input__action-btn--recording {
  background-color: var(--accent-color);
  color: var(--primary-bg);
}

.chat-input__action-btn--recording:hover {
  background-color: var(--accent-hover);
}

.chat-input__send {
  background-color: var(--accent-color);
  border: none;
  color: var(--primary-bg);
  cursor: pointer;
  padding: 12px;
  border-radius: 16px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 48px;
  height: 48px;
}

.chat-input__send:hover:not(:disabled) {
  background-color: var(--accent-hover);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(252, 212, 105, 0.3);
}

.chat-input__send:disabled {
  background-color: var(--text-muted);
  cursor: not-allowed;
  transform: none;
}



/* Auto-resize textarea */
.chat-input__field {
  field-sizing: content;
}

/* AI Disclaimer */
.chat-input__disclaimer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  margin-top: 8px;
  padding: 0 4px;
  color: var(--text-secondary);
  font-size: 12px;
  line-height: 1.4;
}

.chat-input__disclaimer svg {
  flex-shrink: 0;
  opacity: 0.7;
}

.chat-input__disclaimer span {
  opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-input__form {
    border-radius: 20px;
    padding: 14px;
  }

  .chat-input__field {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .chat-input__actions {
    gap: 12px;
    flex-wrap: wrap;
    margin-top: 16px;
    padding-top: 14px;
  }

  .chat-input__action-btn {
    padding: 8px 12px;
    font-size: 14px;
  }

  .chat-input__send {
    min-width: 44px;
    height: 44px;
    padding: 10px;
  }
  .chat-input__disclaimer{
    align-items: normal;
  }
  .chat-input__disclaimer span{
    text-align: justify;
  }
}

/* Loading Spinner */
.chat-input__loading-spinner {
  width: 18px;
  height: 18px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Disabled State */
.chat-input__field:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.chat-input__send:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
