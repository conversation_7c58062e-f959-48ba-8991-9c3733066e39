.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-list--empty {
  justify-content: center;
  align-items: center;
}

.message-list__empty-state {
  text-align: center;
  max-width: 400px;
  padding: 48px 24px;
}

.message-list__empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.message-list__empty-state h3 {
  color: var(--text-primary);
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.message-list__empty-state p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 16px;
}

.message-list__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  gap: 16px;
}

.message-list__loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--accent-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.message-list__loading p {
  color: var(--text-secondary);
  margin: 0;
}

.message-list__load-more {
  display: flex;
  justify-content: center;
  padding: 16px;
}

.message-list__load-more-btn {
  background: var(--secondary-bg);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.message-list__load-more-btn:hover {
  background: var(--hover-bg);
  border-color: var(--accent-color);
}

.message-list__messages {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.message-list__message-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-list__message {
  display: flex;
  gap: 12px;
  max-width: 80%;
}

.message-list__message--user {
  align-self: flex-end;
}

.message-list__message--ai {
  align-self: flex-start;
}

.message-list__message-content {
  flex: 1;
  min-width: 0;
}

.message-list__message--user .message-list__message-content {
  background: var(--card-bg);
  border-radius: 16px;
  padding: 12px 16px;
}

.message-list__message--ai .message-list__message-content {
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
}

.message-list__message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.message-list__message-sender {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
}

.message-list__regenerated-badge {
  font-size: 12px;
  opacity: 0.7;
  margin-left: 4px;
}

.message-list__streaming-indicator {
  display: flex;
  align-items: center;
  gap: 2px;
  margin-left: 8px;
}

.message-list__streaming-dot {
  width: 6px;
  height: 6px;
  background: var(--accent-color);
  border-radius: 50%;
  animation: streaming-pulse 1.4s ease-in-out infinite both;
}

.message-list__streaming-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.message-list__streaming-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes streaming-pulse {
  0%, 80%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  40% {
    opacity: 1;
    transform: scale(1);
  }
}

.message-list__message-footer {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.message-list__message-model {
  font-size: 12px;
  color: var(--text-secondary);
  background: none;
  padding: 0;
  border-radius: 0;
  font-weight: normal;
  text-transform: none;
}

.message-list__message-text {
  color: var(--text-primary);
  line-height: 1.5;
  word-wrap: break-word;
  margin-bottom: 8px;
}

/* Markdown integration styles */
.message-list__markdown {
  margin: 0;
}

.message-list__markdown .markdown-paragraph:last-child {
  margin-bottom: 0;
}

.message-list__markdown .markdown-heading:first-child {
  margin-top: 0;
}

.message-list__markdown .markdown-code-block {
  margin: 0.75em 0;
}

.message-list__markdown .markdown-list {
  margin: 0.5em 0;
}

.message-list__markdown .markdown-blockquote {
  margin: 0.75em 0;
}

/* User message markdown styling */
.message-list__markdown--user .markdown-paragraph {
  margin: 0.25em 0;
}

.message-list__markdown--user .markdown-paragraph:first-child {
  margin-top: 0;
}

.message-list__markdown--user .markdown-paragraph:last-child {
  margin-bottom: 0;
}

.message-list__markdown--user .markdown-code-block {
  margin: 0.5em 0;
}

.message-list__markdown--user .markdown-list {
  margin: 0.25em 0;
}

/* Attachment styles */
.message-list__attachment {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  margin-bottom: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.message-list__attachment-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.message-list__attachment-info {
  flex: 1;
  min-width: 0;
}

.message-list__attachment-name {
  font-size: 14px;
  font-weight: 500;
  color: inherit;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.message-list__attachment-size {
  font-size: 12px;
  opacity: 0.7;
  margin-top: 2px;
}

/* Image attachment styles */
.message-list__attachment-image-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.message-list__attachment-image {
  max-width: 300px;
  max-height: 200px;
  border-radius: 8px;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.message-list__attachment-image:hover {
  transform: scale(1.02);
}

/* File attachment styles */
.message-list__attachment-file {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.message-list__attachment-link {
  color: inherit;
  text-decoration: underline;
  text-decoration-color: rgba(255, 255, 255, 0.5);
}

.message-list__attachment-link:hover {
  text-decoration-color: rgba(255, 255, 255, 0.8);
}

.message-list__message-time {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 4px;
}

.message-list__message-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
  opacity: 1;
  transition: opacity 0.2s ease;
}

.message-list__copy-btn {
  opacity: 1 !important;
}

.message-list__copy-btn[title="Copied!"] {
  color: #28ca42;
}

.message-list__copy-btn[title="Copied!"]:hover {
  color: #28ca42;
}

.message-list__action-btn {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-list__action-btn:hover {
  background: var(--hover-bg);
  color: var(--text-primary);
}

.message-list__action-btn svg {
  width: 16px;
  height: 16px;
}

.message-list__action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.message-list__action-btn--loading {
  opacity: 0.8;
}

/* Small loading spinner for regenerate button */
.message-list__loading-spinner--small {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Error display */
.message-list__error {
  padding: 0 20px;
  position: sticky;
  top: 0;
  max-width: 800px;
  margin: 0 auto;
}

.message-list__error-content {
  background: var(--error-bg, #fee);
  border: 1px solid var(--error-border, #fcc);
  border-radius: 8px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.message-list__error-text {
  color: var(--error-color, #c33);
  font-size: 14px;
  flex: 1;
}

.message-list__error-close {
  background: none;
  border: none;
  color: var(--error-color, #c33);
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.message-list__error-close:hover {
  background-color: var(--error-hover-bg, #fdd);
}

.message-list__error-upgrade-btn {
  background: #fcd469;
  border: none;
  color: #000;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.message-list__error-upgrade-btn:hover {
  background: #f5c842;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .message-list {
    padding: 12px;
  }

  .message-list__message {
    max-width: 90%;
  }

  .message-list__message-avatar {
    width: 32px;
    height: 32px;
    font-size: 10px;
  }

  .message-list__ai-icon {
    font-size: 16px;
  }

  .message-list__message--user .message-list__message-content,
  .message-list__message--ai .message-list__message-content {
    padding: 10px 12px;
  }
}
