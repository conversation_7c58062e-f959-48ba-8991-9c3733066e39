.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--primary-bg);
  position: relative;
}

/* Header */
.chat-area__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 20px;
  background-color: var(--primary-bg);
  min-height: 60px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.chat-area__header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.chat-area__hamburger {
  display: none;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
  align-items: center;
  justify-content: center;
}

.chat-area__hamburger:hover {
  background-color: var(--card-bg);
  color: var(--text-primary);
  transform: scale(1.05);
}



.chat-area__title h1 {
  font-size: 22px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.chat-area__subtitle {
  font-size: 15px;
  color: var(--text-secondary);
  font-weight: 400;
}

.chat-area__share {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-area__share:hover {
  background-color: var(--card-bg);
  color: var(--text-primary);
  transform: scale(1.05);
}

/* Main Content */
.chat-area__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding: 32px;
}

/* Welcome Section */
.chat-area__welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  max-width: 900px;
  margin: 0 auto;
  text-align: center;
  gap: 40px;
}

.chat-area__greeting {
  margin-bottom: 0;
}

.chat-area__greeting h2 {
  font-size: 42px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  line-height: 1.2;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--accent-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Prompt Cards */
.chat-area__prompts {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  width: 100%;
  max-width: 800px;
  justify-content: center;
}

.chat-area__prompt-card {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.chat-area__prompt-card:hover {
  background-color: var(--secondary-bg);
  border-color: var(--accent-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.chat-area__prompt-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}

/* Messages */
.chat-area__messages {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.chat-message {
  display: flex;
  margin-bottom: 16px;
}

.chat-message--user {
  justify-content: flex-end;
}

.chat-message--assistant {
  justify-content: flex-start;
}

.chat-message__content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.5;
}

.chat-message--user .chat-message__content {
  background-color: var(--card-bg);
  color: var(--text-primary);
}

.chat-message--assistant .chat-message__content {
  background-color: var(--secondary-bg);
  color: var(--text-primary);
}

/* Error Display */
.chat-area__error {
  padding: 16px 24px;
  background: rgba(239, 68, 68, 0.1);
  border-top: 1px solid #ef4444;
}

.chat-area__error-content {
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 800px;
  margin: 0 auto;
}

.chat-area__error-icon {
  font-size: 18px;
  flex-shrink: 0;
}

.chat-area__error-message {
  flex: 1;
  color: #ef4444;
  font-size: 14px;
  font-weight: 500;
}

.chat-area__error-close {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.chat-area__error-close:hover {
  background: rgba(239, 68, 68, 0.2);
}

/* Input Container */
.chat-area__input-container {
  padding: 10px;
  background-color: var(--primary-bg);
  position: sticky;
  bottom: 0;
  z-index: 100;
}

/* Welcome Input Container - Centered */
.chat-area__welcome-input-container {
  padding: 0;
  border-top: none;
  background-color: transparent;
  position: static;
  z-index: auto;
  width: 100%;
  max-width: 900px;
}

/* Welcome section subtitle */
.chat-area__welcome-subtitle {
  font-size: 18px;
  color: var(--text-secondary);
  margin-bottom: 32px;
  font-weight: 400;
  line-height: 1.5;
}

/* Smooth transitions for layout changes */
.chat-area__content {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-area__welcome {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: fadeInUp 0.6s ease-out;
}

.chat-area__input-container {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: slideInUp 0.5s ease-out;
}

.chat-area__welcome-input-container {
  animation: fadeInScale 0.5s ease-out;
}

/* Keyframe animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Drag and Drop Overlay */
.chat-area__drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.chat-area__drag-content {
  background: var(--primary-bg, #ffffff);
  border: 2px dashed var(--accent-color, #fcd469);
  border-radius: 16px;
  padding: 48px;
  text-align: center;
  max-width: 400px;
  margin: 20px;
}

.chat-area__drag-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.chat-area__drag-text {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.chat-area__drag-subtext {
  font-size: 14px;
  color: var(--text-secondary);
}

/* Drag Error Display */
.chat-area__drag-error {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1001;
  max-width: 400px;
  width: calc(100% - 40px);
}

.chat-area__drag-error-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid #ef4444;
  border-radius: 8px;
  backdrop-filter: blur(8px);
}

.chat-area__drag-error-icon {
  font-size: 18px;
  flex-shrink: 0;
}

.chat-area__drag-error-message {
  flex: 1;
  color: #ef4444;
  font-size: 14px;
  font-weight: 500;
}

.chat-area__drag-error-close {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.chat-area__drag-error-close:hover {
  background: rgba(239, 68, 68, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-area {
    width: 100%;
    height: 100vh;
  }

  .chat-area__hamburger {
    display: flex;
  }

  .chat-area__header {
    padding: 5px 15px 0 15px;
    min-height: 25px;
  }

  .chat-area__title h1 {
    font-size: 20px;
  }

  .chat-area__subtitle {
    font-size: 14px;
  }

  .chat-area__content {
    padding: 0 5px 5px 5px;
    margin-bottom: 12px;
  }

  .chat-area__greeting h2 {
    font-size: 32px;
  }

  .chat-area__prompts {
    gap: 10px;
  }

  .chat-area__prompt-card {
    padding: 10px 16px;
    font-size: 13px;
  }

  .chat-area__input-container {
    padding: 10px;
  }

  .chat-area__welcome-input-container {
    padding: 0;
    max-width: 100%;
  }

  .chat-area__greeting h2 {
    font-size: 28px;
  }

  .chat-area__welcome {
    gap: 32px;
  }

  .chat-message__content {
    max-width: 85%;
  }
}
